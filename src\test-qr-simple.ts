// Simple QR code test
import { generateQRCodeSVG, generateOTPAuthURL, svgToDataURL } from './utils/twoFactorAuth';

export function testQRCodeGeneration() {
  console.log('🧪 Testing QR Code Generation...');
  
  try {
    // Test data
    const email = '<EMAIL>';
    const issuer = 'TestApp';
    const secret = 'JBSWY3DPEHPK3PXP'; // Base32 encoded secret
    
    // Generate OTP Auth URL
    const otpAuthUrl = generateOTPAuthURL(email, issuer, secret);
    console.log('✅ OTP Auth URL:', otpAuthUrl);
    
    // Generate QR Code
    const qrCodeSVG = generateQRCodeSVG(otpAuthUrl, 256);
    console.log('✅ QR Code SVG generated, length:', qrCodeSVG.length);
    
    // Convert to data URL
    const qrCodeDataUrl = svgToDataURL(qrCodeSVG);
    console.log('✅ QR Code Data URL generated, length:', qrCodeDataUrl.length);
    
    // Check if it contains actual QR code data (not error)
    const isValid = qrCodeSVG.includes('<rect') && !qrCodeSVG.includes('QR Error');
    console.log('✅ QR Code validation:', isValid ? 'VALID' : 'INVALID');
    
    return {
      success: true,
      otpAuthUrl,
      qrCodeSVG,
      qrCodeDataUrl,
      isValid
    };
  } catch (error) {
    console.error('❌ QR Code test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Make it available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testQRCodeGeneration = testQRCodeGeneration;
  console.log('🚀 QR Code test function available: testQRCodeGeneration()');
}
