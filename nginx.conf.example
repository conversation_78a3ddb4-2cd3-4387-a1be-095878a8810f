# مثال على إعداد Nginx لحل مشكلة 404 في React SPA
# ضع هذا الإعداد في ملف تكوين Nginx الخاص بك

server {
    listen 80;
    server_name your-domain.com;
    root /path/to/your/dist/folder;
    index index.html;

    # Handle client-side routing
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Cache static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
}