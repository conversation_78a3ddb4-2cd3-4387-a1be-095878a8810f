<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار QR Code</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .qr-display {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
        }
        .qr-display img {
            max-width: 256px;
            border: 1px solid #ccc;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 اختبار QR Code للمصادقة الثنائية</h1>
        
        <div class="test-section">
            <h3>📱 اختبار إنشاء QR Code</h3>
            <p>اختبار إنشاء QR Code باستخدام الطرق المختلفة</p>
            
            <button onclick="testQRGeneration()">اختبار إنشاء QR Code</button>
            <button onclick="testWithSampleData()">اختبار مع بيانات تجريبية</button>
            <button onclick="clearResults()">مسح النتائج</button>
            
            <div id="qr-results"></div>
            <div id="log-output" class="log"></div>
        </div>
        
        <div class="test-section">
            <h3>🌐 فحص توافق المتصفح</h3>
            <button onclick="checkBrowserSupport()">فحص التوافق</button>
            <div id="browser-results"></div>
        </div>
    </div>

    <script>
        // Override console.log to display in our log area
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToLog(message, type = 'log') {
            const logOutput = document.getElementById('log-output');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'warn' ? 'warning' : 'success';
            logOutput.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addToLog(args.join(' '), 'warn');
        };

        // QR Code generation functions (simplified for testing)
        function generateTestQR(text, method = 'svg') {
            try {
                if (method === 'canvas') {
                    return generateCanvasQR(text);
                } else {
                    return generateSVGQR(text);
                }
            } catch (error) {
                console.error('خطأ في إنشاء QR Code:', error);
                return null;
            }
        }
        
        function generateSVGQR(text) {
            // Placeholder SVG QR (for demonstration)
            const size = 256;
            return `<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${size} ${size}">
                <rect width="${size}" height="${size}" fill="white"/>
                <rect x="20" y="20" width="40" height="40" fill="black"/>
                <rect x="${size-60}" y="20" width="40" height="40" fill="black"/>
                <rect x="20" y="${size-60}" width="40" height="40" fill="black"/>
                <text x="${size/2}" y="${size/2}" text-anchor="middle" dominant-baseline="middle" font-family="Arial" font-size="14" fill="black">
                    QR Test
                </text>
                <text x="${size/2}" y="${size/2 + 20}" text-anchor="middle" dominant-baseline="middle" font-family="Arial" font-size="10" fill="gray">
                    ${text.substring(0, 20)}...
                </text>
            </svg>`;
        }
        
        function generateCanvasQR(text) {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const size = 256;
            
            canvas.width = size;
            canvas.height = size;
            
            // White background
            ctx.fillStyle = 'white';
            ctx.fillRect(0, 0, size, size);
            
            // Simple pattern
            ctx.fillStyle = 'black';
            ctx.fillRect(20, 20, 40, 40);
            ctx.fillRect(size-60, 20, 40, 40);
            ctx.fillRect(20, size-60, 40, 40);
            
            // Text
            ctx.fillStyle = 'black';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('Canvas QR', size/2, size/2);
            
            return canvas.toDataURL('image/png');
        }

        function testQRGeneration() {
            console.log('🧪 بدء اختبار إنشاء QR Code...');
            
            const testText = 'otpauth://totp/TestApp:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=TestApp';
            const resultsDiv = document.getElementById('qr-results');
            
            // Test SVG
            console.log('📱 اختبار SVG QR Code...');
            const svgQR = generateTestQR(testText, 'svg');
            if (svgQR) {
                console.log('✅ تم إنشاء SVG QR Code بنجاح');
                const svgDataUrl = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgQR)));
                resultsDiv.innerHTML += `
                    <div class="qr-display">
                        <h4>SVG QR Code</h4>
                        <img src="${svgDataUrl}" alt="SVG QR Code">
                    </div>
                `;
            }
            
            // Test Canvas
            console.log('🖼️ اختبار Canvas QR Code...');
            try {
                const canvasQR = generateTestQR(testText, 'canvas');
                if (canvasQR) {
                    console.log('✅ تم إنشاء Canvas QR Code بنجاح');
                    resultsDiv.innerHTML += `
                        <div class="qr-display">
                            <h4>Canvas QR Code</h4>
                            <img src="${canvasQR}" alt="Canvas QR Code">
                        </div>
                    `;
                }
            } catch (error) {
                console.error('❌ فشل في إنشاء Canvas QR Code:', error);
            }
            
            console.log('🎉 انتهى اختبار QR Code');
        }
        
        function testWithSampleData() {
            console.log('📝 اختبار مع بيانات تجريبية...');
            
            const sampleData = [
                'otpauth://totp/Pegasus:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=Pegasus',
                'otpauth://totp/TestApp:<EMAIL>?secret=ABCDEFGHIJKLMNOP&issuer=TestApp',
                'https://example.com/test'
            ];
            
            const resultsDiv = document.getElementById('qr-results');
            resultsDiv.innerHTML = '<h4>نتائج البيانات التجريبية:</h4>';
            
            sampleData.forEach((data, index) => {
                console.log(`اختبار البيانات ${index + 1}: ${data.substring(0, 30)}...`);
                const qr = generateTestQR(data, 'svg');
                if (qr) {
                    const dataUrl = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(qr)));
                    resultsDiv.innerHTML += `
                        <div class="qr-display">
                            <h5>عينة ${index + 1}</h5>
                            <img src="${dataUrl}" alt="Sample QR ${index + 1}">
                            <p style="font-size: 12px; color: #666;">${data.substring(0, 50)}...</p>
                        </div>
                    `;
                }
            });
        }
        
        function checkBrowserSupport() {
            console.log('🌐 فحص دعم المتصفح...');
            
            const features = {
                'crypto': !!window.crypto,
                'crypto.getRandomValues': !!(window.crypto && window.crypto.getRandomValues),
                'crypto.subtle': !!(window.crypto && window.crypto.subtle),
                'Canvas': !!document.createElement('canvas').getContext,
                'btoa': !!window.btoa,
                'URLSearchParams': !!window.URLSearchParams
            };
            
            const resultsDiv = document.getElementById('browser-results');
            let html = '<h4>نتائج فحص التوافق:</h4><ul>';
            
            Object.entries(features).forEach(([feature, supported]) => {
                const status = supported ? '✅ مدعوم' : '❌ غير مدعوم';
                const color = supported ? 'green' : 'red';
                html += `<li style="color: ${color};">${feature}: ${status}</li>`;
                console.log(`${feature}: ${status}`);
            });
            
            html += '</ul>';
            resultsDiv.innerHTML = html;
            
            const allSupported = Object.values(features).every(Boolean);
            console.log(`النتيجة النهائية: ${allSupported ? '✅ جميع الميزات مدعومة' : '⚠️ بعض الميزات غير مدعومة'}`);
        }
        
        function clearResults() {
            document.getElementById('qr-results').innerHTML = '';
            document.getElementById('browser-results').innerHTML = '';
            document.getElementById('log-output').innerHTML = '';
            console.log('🧹 تم مسح النتائج');
        }
        
        // Initialize
        console.log('🚀 صفحة اختبار QR Code جاهزة');
        console.log('يمكنك استخدام الأزرار أعلاه لاختبار الوظائف المختلفة');
    </script>
</body>
</html>
