// Enhanced 2FA functions using custom implementation
import { supabase } from './client';
import { 
  generateSecret, 
  generateOTPAuthURL, 
  generateQRCodeSVG, 
  svgToDataURL, 
  verifyTOTP 
} from '@/utils/twoFactorAuth';

// Generate 2FA secret with custom implementation
export const generate2FASecret = async (userId: string, email: string) => {
  try {
    console.log('Starting 2FA secret generation for user:', userId);
    
    // Generate a random secret using our custom function
    const secret = generateSecret();
    console.log('Generated secret successfully');
    
    // Create the OTP Auth URL
    const otpAuthUrl = generateOTPAuthURL(email, 'Pegasus Tools', secret);
    console.log('Generated OTP Auth URL');
    
    // Generate QR code as SVG
    const qrCodeSVG = generateQRCodeSVG(otpAuthUrl, 256);
    const qrCodeDataUrl = svgToDataURL(qrCodeSVG);
    console.log('Generated QR code successfully');
    
    // Store the secret in the database
    const { error } = await supabase
      .from('users')
      .update({ 
        otp_secret: secret,
        two_factor_enabled: false // Will be enabled after verification
      })
      .eq('id', userId);
    
    if (error) {
      console.error('Database error:', error);
      throw error;
    }
    
    console.log('Secret stored in database successfully');
    
    return {
      secret,
      qrCodeDataUrl,
      otpAuthUrl
    };
  } catch (error) {
    console.error('Error in generate2FASecret:', error);
    throw error;
  }
};

// Verify 2FA token with custom implementation
export const verify2FAToken = async (userId: string, token: string) => {
  try {
    console.log('Starting 2FA token verification for user:', userId);
    
    // Get the user's secret from the database
    const { data: userData, error } = await supabase
      .from('users')
      .select('otp_secret')
      .eq('id', userId)
      .single();
    
    if (error) {
      console.error('Database error while fetching user data:', error);
      return false;
    }
    
    if (!userData?.otp_secret) {
      console.error('No OTP secret found for user');
      return false;
    }
    
    console.log('Retrieved user secret from database');
    
    // Verify the token using our custom TOTP implementation
    const isValid = await verifyTOTP(token, userData.otp_secret);
    console.log('Token verification result:', isValid);
    
    if (isValid) {
      // Enable 2FA for the user
      const { error: updateError } = await supabase
        .from('users')
        .update({ two_factor_enabled: true })
        .eq('id', userId);
      
      if (updateError) {
        console.error('Error enabling 2FA:', updateError);
        return false;
      }
      
      console.log('2FA enabled successfully for user');
    }
    
    return isValid;
  } catch (error) {
    console.error('Error in verify2FAToken:', error);
    return false;
  }
};

// Validate 2FA token for login with custom implementation
export const validate2FAToken = async (userId: string, token: string) => {
  try {
    console.log('Starting 2FA token validation for login, user:', userId);
    
    // Get the user's secret from the database
    const { data: userData, error } = await supabase
      .from('users')
      .select('otp_secret, two_factor_enabled')
      .eq('id', userId)
      .single();
    
    if (error) {
      console.error('Database error while fetching user data:', error);
      return false;
    }
    
    if (!userData?.otp_secret) {
      console.error('No OTP secret found for user');
      return false;
    }
    
    if (!userData.two_factor_enabled) {
      console.error('2FA is not enabled for this user');
      return false;
    }
    
    console.log('Retrieved user data, 2FA is enabled');
    
    // Verify the token using our custom TOTP implementation
    const isValid = await verifyTOTP(token, userData.otp_secret);
    console.log('Login token verification result:', isValid);
    
    return isValid;
  } catch (error) {
    console.error('Error in validate2FAToken:', error);
    return false;
  }
};

// Disable 2FA
export const disable2FA = async (userId: string) => {
  try {
    console.log('Disabling 2FA for user:', userId);
    
    // Remove the secret and disable 2FA
    const { error } = await supabase
      .from('users')
      .update({ 
        otp_secret: null,
        two_factor_enabled: false
      })
      .eq('id', userId);
    
    if (error) {
      console.error('Error disabling 2FA:', error);
      return false;
    }
    
    console.log('2FA disabled successfully');
    return true;
  } catch (error) {
    console.error('Error in disable2FA:', error);
    return false;
  }
};

// Save QR code file
export const saveQRCodeFile = async (userId: string, qrCodeDataUrl: string) => {
  try {
    console.log('Saving QR code file for user:', userId);
    
    // Convert data URL to blob
    const response = await fetch(qrCodeDataUrl);
    const blob = await response.blob();
    
    // Create a download link
    const url = URL.createObjectURL(blob);
    
    console.log('QR code file prepared successfully');
    
    return {
      success: true,
      url: url
    };
  } catch (error) {
    console.error('Error saving QR code file:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

// Check if browser supports required features
export const checkBrowserCompatibility = (): { supported: boolean; missingFeatures: string[] } => {
  const missingFeatures: string[] = [];
  
  // Check for crypto.getRandomValues
  if (!crypto || !crypto.getRandomValues) {
    missingFeatures.push('crypto.getRandomValues');
  }
  
  // Check for crypto.subtle
  if (!crypto || !crypto.subtle) {
    missingFeatures.push('crypto.subtle (Web Crypto API)');
  }
  
  // Check for basic APIs
  if (!btoa) {
    missingFeatures.push('btoa (Base64 encoding)');
  }
  
  if (!URLSearchParams) {
    missingFeatures.push('URLSearchParams');
  }
  
  return {
    supported: missingFeatures.length === 0,
    missingFeatures
  };
};

// Test TOTP generation (for debugging)
export const testTOTPGeneration = async (secret?: string) => {
  try {
    const { generateTOTP } = await import('@/utils/twoFactorAuth');

    const testSecret = secret || generateSecret();
    console.log('Test secret:', testSecret);

    const token = await generateTOTP(testSecret);
    console.log('Generated test token:', token);

    const isValid = await verifyTOTP(token, testSecret);
    console.log('Token validation result:', isValid);

    return {
      secret: testSecret,
      token,
      isValid
    };
  } catch (error) {
    console.error('Error in test TOTP generation:', error);
    throw error;
  }
};
