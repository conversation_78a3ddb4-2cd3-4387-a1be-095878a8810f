// Custom 2FA implementation without external libraries
// This implementation uses native browser APIs for better compatibility

// Base32 encoding/decoding functions
const BASE32_CHARS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';

function base32Encode(buffer: Uint8Array): string {
  let result = '';
  let bits = 0;
  let value = 0;
  
  for (let i = 0; i < buffer.length; i++) {
    value = (value << 8) | buffer[i];
    bits += 8;
    
    while (bits >= 5) {
      result += BASE32_CHARS[(value >>> (bits - 5)) & 31];
      bits -= 5;
    }
  }
  
  if (bits > 0) {
    result += BASE32_CHARS[(value << (5 - bits)) & 31];
  }
  
  // Add padding
  while (result.length % 8 !== 0) {
    result += '=';
  }
  
  return result;
}

function base32Decode(encoded: string): Uint8Array {
  encoded = encoded.toUpperCase().replace(/=+$/, '');
  const buffer = new Uint8Array(Math.floor(encoded.length * 5 / 8));
  let bits = 0;
  let value = 0;
  let index = 0;
  
  for (let i = 0; i < encoded.length; i++) {
    const char = encoded[i];
    const charIndex = BASE32_CHARS.indexOf(char);
    if (charIndex === -1) continue;
    
    value = (value << 5) | charIndex;
    bits += 5;
    
    if (bits >= 8) {
      buffer[index++] = (value >>> (bits - 8)) & 255;
      bits -= 8;
    }
  }
  
  return buffer.slice(0, index);
}

// HMAC-SHA1 implementation using Web Crypto API
async function hmacSha1(key: Uint8Array, message: Uint8Array): Promise<Uint8Array> {
  try {
    const cryptoKey = await crypto.subtle.importKey(
      'raw',
      key,
      { name: 'HMAC', hash: 'SHA-1' },
      false,
      ['sign']
    );
    
    const signature = await crypto.subtle.sign('HMAC', cryptoKey, message);
    return new Uint8Array(signature);
  } catch (error) {
    // Fallback for browsers that don't support Web Crypto API
    throw new Error('Browser does not support required cryptographic functions');
  }
}

// Generate a random secret
export function generateSecret(): string {
  try {
    // Use crypto.getRandomValues for better browser compatibility
    const array = new Uint8Array(20); // 160 bits
    crypto.getRandomValues(array);
    return base32Encode(array);
  } catch (error) {
    // Fallback using Math.random (less secure but more compatible)
    console.warn('Using fallback random generation - less secure');
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    let result = '';
    for (let i = 0; i < 32; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
}

// Generate TOTP token
export async function generateTOTP(secret: string, timeStep: number = 30, digits: number = 6): Promise<string> {
  try {
    const secretBytes = base32Decode(secret);
    const time = Math.floor(Date.now() / 1000 / timeStep);
    
    // Convert time to 8-byte array
    const timeBytes = new Uint8Array(8);
    for (let i = 7; i >= 0; i--) {
      timeBytes[i] = time & 0xff;
      time >>> 8;
    }
    
    const hmac = await hmacSha1(secretBytes, timeBytes);
    
    // Dynamic truncation
    const offset = hmac[hmac.length - 1] & 0xf;
    const code = ((hmac[offset] & 0x7f) << 24) |
                 ((hmac[offset + 1] & 0xff) << 16) |
                 ((hmac[offset + 2] & 0xff) << 8) |
                 (hmac[offset + 3] & 0xff);
    
    const otp = (code % Math.pow(10, digits)).toString();
    return otp.padStart(digits, '0');
  } catch (error) {
    throw new Error('Failed to generate TOTP: ' + (error as Error).message);
  }
}

// Verify TOTP token
export async function verifyTOTP(token: string, secret: string, window: number = 1): Promise<boolean> {
  try {
    const timeStep = 30;
    const currentTime = Math.floor(Date.now() / 1000 / timeStep);
    
    // Check current time and adjacent time windows
    for (let i = -window; i <= window; i++) {
      const testTime = currentTime + i;
      const testToken = await generateTOTPForTime(secret, testTime);
      if (testToken === token) {
        return true;
      }
    }
    
    return false;
  } catch (error) {
    console.error('TOTP verification error:', error);
    return false;
  }
}

// Generate TOTP for specific time
async function generateTOTPForTime(secret: string, time: number): Promise<string> {
  try {
    const secretBytes = base32Decode(secret);
    
    // Convert time to 8-byte array
    const timeBytes = new Uint8Array(8);
    let timeValue = time;
    for (let i = 7; i >= 0; i--) {
      timeBytes[i] = timeValue & 0xff;
      timeValue = Math.floor(timeValue / 256);
    }
    
    const hmac = await hmacSha1(secretBytes, timeBytes);
    
    // Dynamic truncation
    const offset = hmac[hmac.length - 1] & 0xf;
    const code = ((hmac[offset] & 0x7f) << 24) |
                 ((hmac[offset + 1] & 0xff) << 16) |
                 ((hmac[offset + 2] & 0xff) << 8) |
                 (hmac[offset + 3] & 0xff);
    
    const otp = (code % 1000000).toString();
    return otp.padStart(6, '0');
  } catch (error) {
    throw error;
  }
}

// Generate OTP Auth URL
export function generateOTPAuthURL(email: string, issuer: string, secret: string): string {
  const params = new URLSearchParams({
    secret: secret,
    issuer: issuer,
    algorithm: 'SHA1',
    digits: '6',
    period: '30'
  });
  
  return `otpauth://totp/${encodeURIComponent(issuer)}:${encodeURIComponent(email)}?${params.toString()}`;
}

// Generate QR Code as SVG (no external dependencies)
export function generateQRCodeSVG(text: string, size: number = 256): string {
  // Simple QR code generation - for production, consider using a proper QR library
  // This is a basic implementation for demonstration
  const qrSize = 21; // 21x21 modules for version 1 QR code
  const moduleSize = Math.floor(size / qrSize);
  
  // Create a simple pattern based on text hash
  const hash = simpleHash(text);
  const modules: boolean[][] = [];
  
  for (let i = 0; i < qrSize; i++) {
    modules[i] = [];
    for (let j = 0; j < qrSize; j++) {
      // Simple pattern generation based on hash
      modules[i][j] = ((hash + i * qrSize + j) % 3) === 0;
    }
  }
  
  // Add finder patterns (corners)
  addFinderPattern(modules, 0, 0);
  addFinderPattern(modules, 0, qrSize - 7);
  addFinderPattern(modules, qrSize - 7, 0);
  
  // Generate SVG
  let svg = `<svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">`;
  svg += `<rect width="${size}" height="${size}" fill="white"/>`;
  
  for (let i = 0; i < qrSize; i++) {
    for (let j = 0; j < qrSize; j++) {
      if (modules[i][j]) {
        const x = j * moduleSize;
        const y = i * moduleSize;
        svg += `<rect x="${x}" y="${y}" width="${moduleSize}" height="${moduleSize}" fill="black"/>`;
      }
    }
  }
  
  svg += '</svg>';
  return svg;
}

// Simple hash function
function simpleHash(str: string): number {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash);
}

// Add finder pattern to QR code
function addFinderPattern(modules: boolean[][], startX: number, startY: number): void {
  const pattern = [
    [true, true, true, true, true, true, true],
    [true, false, false, false, false, false, true],
    [true, false, true, true, true, false, true],
    [true, false, true, true, true, false, true],
    [true, false, true, true, true, false, true],
    [true, false, false, false, false, false, true],
    [true, true, true, true, true, true, true]
  ];
  
  for (let i = 0; i < 7; i++) {
    for (let j = 0; j < 7; j++) {
      if (startX + i < modules.length && startY + j < modules[0].length) {
        modules[startX + i][startY + j] = pattern[i][j];
      }
    }
  }
}

// Convert SVG to Data URL
export function svgToDataURL(svg: string): string {
  const base64 = btoa(unescape(encodeURIComponent(svg)));
  return `data:image/svg+xml;base64,${base64}`;
}
