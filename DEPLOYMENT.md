# إعدادات النشر لحل مشكلة 404 عند تحديث الصفحة

هذا المشروع هو Single Page Application (SPA) يستخدم React Router. لحل مشكلة ظهور خطأ 404 عند تحديث الصفحة أو الدخول مباشرة لرابط فرعي، تحتاج إلى إعداد إعادة التوجيه (fallback routing) حسب منصة الاستضافة:

## 1. Netlify
استخدم الملف الموجود: `public/_redirects`
```
/*    /index.html   200
```

## 2. Vercel
استخدم الملف الموجود: `vercel.json`
```json
{
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ]
}
```

## 3. Apache Server
استخدم الملف الموجود: `public/.htaccess`
```apache
Options -MultiViews
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^ index.html [QSA,L]
```

## 4. Nginx
أضف هذا الإعداد في ملف تكوين Nginx:
```nginx
location / {
  try_files $uri $uri/ /index.html;
}
```

## 5. GitHub Pages
استخدم الملف الموجود: `public/404.html`
(يتم إعادة التوجيه تلقائياً عبر JavaScript)

## 6. Firebase Hosting
استخدم الملف الموجود: `firebase.json.example`
```json
{
  "hosting": {
    "public": "dist",
    "rewrites": [
      {
        "source": "**",
        "destination": "/index.html"
      }
    ]
  }
}
```

## 7. Nginx Server
استخدم الملف الموجود: `nginx.conf.example`
```nginx
location / {
  try_files $uri $uri/ /index.html;
}
```

## 8. AWS S3 + CloudFront
في إعدادات CloudFront، أضف Custom Error Page:
- HTTP Error Code: 404
- Error Caching Minimum TTL: 0
- Customize Error Response: Yes
- Response Page Path: /index.html
- HTTP Response Code: 200

## التطوير المحلي
تم تحديث `vite.config.ts` لإضافة `historyApiFallback: true` لحل المشكلة في بيئة التطوير.

## سبب المشكلة
عندما يحدث المستخدم الصفحة أو يدخل رابط مباشر، المتصفح يرسل طلب HTTP للخادم للمسار المطلوب. لكن في SPA، هذه المسارات لا توجد كملفات فعلية على الخادم، بل يتم التعامل معها بواسطة React Router في المتصفح. لذلك نحتاج إعادة توجيه جميع الطلبات إلى index.html ليتولى React Router التوجيه الصحيح.