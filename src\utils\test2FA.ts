// Test file for 2FA functionality
import {
  generateSecret,
  generateTOTP,
  verifyTOTP,
  generateOTPAuthURL,
  generateQRCodeSVG,
  generateQRCodeCanvas,
  svgToDataURL
} from './twoFactorAuth';

// Test the complete 2FA flow
export async function test2FAFlow() {
  console.log('🧪 Starting 2FA functionality test...');
  
  try {
    // Step 1: Generate secret
    console.log('📝 Step 1: Generating secret...');
    const secret = generateSecret();
    console.log('✅ Secret generated:', secret);
    
    // Step 2: Generate OTP Auth URL
    console.log('🔗 Step 2: Generating OTP Auth URL...');
    const otpAuthUrl = generateOTPAuthURL('<EMAIL>', 'Test App', secret);
    console.log('✅ OTP Auth URL:', otpAuthUrl);
    
    // Step 3: Generate QR Code (SVG)
    console.log('📱 Step 3a: Generating QR Code (SVG)...');
    const qrCodeSVG = generateQRCodeSVG(otpAuthUrl, 256);
    const qrCodeDataUrlSVG = svgToDataURL(qrCodeSVG);
    console.log('✅ QR Code SVG generated (length):', qrCodeDataUrlSVG.length);

    // Step 3b: Generate QR Code (Canvas)
    console.log('📱 Step 3b: Generating QR Code (Canvas)...');
    let qrCodeDataUrlCanvas = '';
    try {
      qrCodeDataUrlCanvas = generateQRCodeCanvas(otpAuthUrl, 256);
      console.log('✅ QR Code Canvas generated (length):', qrCodeDataUrlCanvas.length);
    } catch (error) {
      console.warn('⚠️ Canvas QR generation failed:', error);
    }

    const qrCodeDataUrl = qrCodeDataUrlCanvas || qrCodeDataUrlSVG;
    
    // Step 4: Generate TOTP token
    console.log('🔢 Step 4: Generating TOTP token...');
    const token = await generateTOTP(secret);
    console.log('✅ TOTP token generated:', token);
    
    // Step 5: Verify TOTP token
    console.log('✔️ Step 5: Verifying TOTP token...');
    const isValid = await verifyTOTP(token, secret);
    console.log('✅ Token verification result:', isValid);
    
    // Step 6: Test with wrong token
    console.log('❌ Step 6: Testing with wrong token...');
    const wrongToken = '123456';
    const isWrongValid = await verifyTOTP(wrongToken, secret);
    console.log('✅ Wrong token verification result:', isWrongValid);
    
    console.log('🎉 All tests completed successfully!');
    
    return {
      success: true,
      secret,
      otpAuthUrl,
      qrCodeDataUrl,
      token,
      isValid,
      isWrongValid
    };
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Test browser compatibility
export function testBrowserCompatibility() {
  console.log('🌐 Testing browser compatibility...');
  
  const features = {
    crypto: !!crypto,
    getRandomValues: !!(crypto && crypto.getRandomValues),
    subtle: !!(crypto && crypto.subtle),
    btoa: !!btoa,
    URLSearchParams: !!URLSearchParams,
    fetch: !!fetch
  };
  
  console.log('Browser features:', features);
  
  const allSupported = Object.values(features).every(Boolean);
  console.log('All features supported:', allSupported);
  
  return {
    supported: allSupported,
    features
  };
}

// Test QR Code generation specifically
export function testQRCodeGeneration() {
  console.log('🔍 Testing QR Code generation...');

  const testText = 'otpauth://totp/Test:<EMAIL>?secret=JBSWY3DPEHPK3PXP&issuer=Test';

  try {
    // Test SVG generation
    console.log('📱 Testing SVG QR Code...');
    const svgQR = generateQRCodeSVG(testText, 256);
    const svgDataUrl = svgToDataURL(svgQR);
    console.log('✅ SVG QR Code generated successfully');
    console.log('SVG length:', svgQR.length);
    console.log('SVG Data URL length:', svgDataUrl.length);

    // Test Canvas generation
    console.log('🖼️ Testing Canvas QR Code...');
    let canvasDataUrl = '';
    try {
      canvasDataUrl = generateQRCodeCanvas(testText, 256);
      console.log('✅ Canvas QR Code generated successfully');
      console.log('Canvas Data URL length:', canvasDataUrl.length);
    } catch (error) {
      console.warn('⚠️ Canvas QR generation failed:', error);
    }

    return {
      success: true,
      svgQR,
      svgDataUrl,
      canvasDataUrl
    };
  } catch (error) {
    console.error('❌ QR Code test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Run tests if this file is imported directly
if (typeof window !== 'undefined') {
  // Only run in browser environment
  console.log('🚀 2FA Test Suite Ready');

  // You can call these functions from browser console:
  // test2FAFlow()
  // testBrowserCompatibility()
  // testQRCodeGeneration()

  // Make functions available globally for testing
  (window as any).test2FAFlow = test2FAFlow;
  (window as any).testBrowserCompatibility = testBrowserCompatibility;
  (window as any).testQRCodeGeneration = testQRCodeGeneration;
}
