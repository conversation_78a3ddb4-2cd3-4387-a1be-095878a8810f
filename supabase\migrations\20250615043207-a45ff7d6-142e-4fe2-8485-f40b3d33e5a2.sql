
-- Enable RLS on all tables that don't have it yet (safe to run multiple times)
ALTER TABLE public.certsave ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.discounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.operations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.pricing ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.supported_models ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.offers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.update ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.two_factor_verification ENABLE ROW LEVEL SECURITY;

-- Create security definer function to check if user is admin (replace if exists)
CREATE OR REPLACE FUNCTION public.is_user_admin()
RETURNS boolean
LANGUAGE sql
STABLE SECURITY DEFINER
SET search_path TO 'public'
AS $$
  SELECT EXISTS (
    SELECT 1 FROM public.users
    WHERE id = auth.uid()
      AND email_type = 'Admin'
  );
$$;

-- Create security definer function to get current user's uid (replace if exists)
CREATE OR REPLACE FUNCTION public.get_current_user_uid()
RETURNS uuid
LANGUAGE sql
STABLE SECURITY DEFINER
SET search_path TO 'public'
AS $$
  SELECT uid FROM public.users WHERE id = auth.uid();
$$;

-- Drop all existing policies that might conflict
DROP POLICY IF EXISTS "Users can view their own operations" ON public.operations;
DROP POLICY IF EXISTS "Users can insert their own operations" ON public.operations;
DROP POLICY IF EXISTS "Admins can view all operations" ON public.operations;
DROP POLICY IF EXISTS "Users can view their own cert files" ON public.certsave;
DROP POLICY IF EXISTS "Users can insert their own cert files" ON public.certsave;
DROP POLICY IF EXISTS "Users can update their own cert files" ON public.certsave;
DROP POLICY IF EXISTS "Users can delete their own cert files" ON public.certsave;
DROP POLICY IF EXISTS "Admins can view all cert files" ON public.certsave;
DROP POLICY IF EXISTS "Users can view their own discounts" ON public.discounts;
DROP POLICY IF EXISTS "Admins can manage all discounts" ON public.discounts;

-- RLS Policies for certsave table (user-specific data)
CREATE POLICY "Users can view their own cert files"
  ON public.certsave
  FOR SELECT
  TO authenticated
  USING (uid = public.get_current_user_uid());

CREATE POLICY "Users can insert their own cert files"
  ON public.certsave
  FOR INSERT
  TO authenticated
  WITH CHECK (uid = public.get_current_user_uid());

CREATE POLICY "Users can update their own cert files"
  ON public.certsave
  FOR UPDATE
  TO authenticated
  USING (uid = public.get_current_user_uid());

CREATE POLICY "Users can delete their own cert files"
  ON public.certsave
  FOR DELETE
  TO authenticated
  USING (uid = public.get_current_user_uid());

CREATE POLICY "Admins can view all cert files"
  ON public.certsave
  FOR ALL
  TO authenticated
  USING (public.is_user_admin());

-- RLS Policies for discounts table (user-specific data)
CREATE POLICY "Users can view their own discounts"
  ON public.discounts
  FOR SELECT
  TO authenticated
  USING (uid = public.get_current_user_uid());

CREATE POLICY "Admins can manage all discounts"
  ON public.discounts
  FOR ALL
  TO authenticated
  USING (public.is_user_admin());

-- RLS Policies for operations table (user-specific data)
CREATE POLICY "Users can view their own operations"
  ON public.operations
  FOR SELECT
  TO authenticated
  USING (uid = public.get_current_user_uid());

CREATE POLICY "Users can insert their own operations"
  ON public.operations
  FOR INSERT
  TO authenticated
  WITH CHECK (uid = public.get_current_user_uid());

CREATE POLICY "Admins can view all operations"
  ON public.operations
  FOR ALL
  TO authenticated
  USING (public.is_user_admin());

-- RLS Policies for admin-only tables
CREATE POLICY "Only admins can access settings"
  ON public.settings
  FOR ALL
  TO authenticated
  USING (public.is_user_admin());

CREATE POLICY "Only admins can access groups"
  ON public.groups
  FOR ALL
  TO authenticated
  USING (public.is_user_admin());

CREATE POLICY "Only admins can manage pricing"
  ON public.pricing
  FOR ALL
  TO authenticated
  USING (public.is_user_admin());

CREATE POLICY "Only admins can manage payment methods"
  ON public.payment_methods
  FOR ALL
  TO authenticated
  USING (public.is_user_admin());

CREATE POLICY "Only admins can manage updates"
  ON public.update
  FOR ALL
  TO authenticated
  USING (public.is_user_admin());

-- RLS Policies for public readable data
CREATE POLICY "Everyone can read supported models"
  ON public.supported_models
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Only admins can insert supported models"
  ON public.supported_models
  FOR INSERT
  TO authenticated
  WITH CHECK (public.is_user_admin());

CREATE POLICY "Only admins can update supported models"
  ON public.supported_models
  FOR UPDATE
  TO authenticated
  USING (public.is_user_admin());

CREATE POLICY "Only admins can delete supported models"
  ON public.supported_models
  FOR DELETE
  TO authenticated
  USING (public.is_user_admin());

-- RLS Policies for offers
CREATE POLICY "Everyone can read offers"
  ON public.offers
  FOR SELECT
  TO authenticated
  USING (true);

CREATE POLICY "Only admins can insert offers"
  ON public.offers
  FOR INSERT
  TO authenticated
  WITH CHECK (public.is_user_admin());

CREATE POLICY "Only admins can update offers"
  ON public.offers
  FOR UPDATE
  TO authenticated
  USING (public.is_user_admin());

CREATE POLICY "Only admins can delete offers"
  ON public.offers
  FOR DELETE
  TO authenticated
  USING (public.is_user_admin());

-- RLS Policies for two_factor_verification (user-specific)
CREATE POLICY "Users can manage their own 2FA verification"
  ON public.two_factor_verification
  FOR ALL
  TO authenticated
  USING (user_id = auth.uid());
